<?php

namespace App\Nova\Actions;

use App\Http\Controllers\Api\ReportGeneratorController;
use App\Order;
use App\Product;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Rap2hpoutre\FastExcel\FastExcel;

class TrendingProductsExport extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Export Trending Products';

    /**
     * Perform the action on the given models.
     *
     * @param ActionFields $fields
     * @param Collection $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $filterType = $fields->filter_type;
        $filename = 'trending-products-' . now()->format('Y-m-d-H-i-s') . '.csv';
        
        $data = $this->getTrendingProductsData($fields, $filterType);
        dispatch(function () use ($fields, $filterType, $filename) {
            $data = $this->getTrendingProductsData($fields, $filterType);
            (new FastExcel($data))->export(storage_path("app/exports.csv"));
            (new ReportGeneratorController)->create('Trending Products Export');
        })->onQueue('hello');

        return Action::message('Your trending products export has started! When done, you will find it by exports');
    }

    /**
     * Get trending products data based on filter type
     */
    private function getTrendingProductsData(ActionFields $fields, string $filterType)
    {
        if ($filterType === 'date_range') {
            return $this->getTrendingProductsByDateRange($fields->start_date, $fields->end_date);
        } else {
            return $this->getTrendingProductsByDayRanges($fields->day_ranges);
        }
    }

    /**
     * Get trending products for a specific date range
     */
    private function getTrendingProductsByDateRange($startDate, $endDate)
    {
        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();
        
        // Header row with date range
        $header = [
            'id' => "From: {$start->format('Y-m-d')} To: {$end->format('Y-m-d')}",
            'title' => '',
            'sku' => '',
            'barcode' => '',
            'meta.isbn_13' => '',
            'sales_count' => ''
        ];

        $trendingProducts = $this->getProductFrequencies($start, $end);
        
        return collect([$header])->concat($trendingProducts);
    }

    /**
     * Get trending products for multiple day ranges
     */
    private function getTrendingProductsByDayRanges($dayRanges)
    {
        $ranges = array_map('trim', explode(',', $dayRanges));
        $data = collect();
        
        // Add header showing the day ranges
        $rangeText = collect($ranges)->map(fn($days) => "Last {$days} days")->join(', ');
        $header = [
            'id' => $rangeText,
            'title' => '',
            'sku' => '',
            'barcode' => '',
            'meta.isbn_13' => '',
            'sales_count' => ''
        ];
        $data->push($header);

        foreach ($ranges as $days) {
            $days = (int) $days;
            $start = Carbon::now()->subDays($days)->startOfDay();
            $end = Carbon::now()->endOfDay();
            
            // Section header for this day range
            $sectionHeader = [
                'id' => "Last {$days} days:",
                'title' => '',
                'sku' => '',
                'barcode' => '',
                'meta.isbn_13' => '',
                'sales_count' => ''
            ];
            $data->push($sectionHeader);
            
            $trendingProducts = $this->getProductFrequencies($start, $end);
            $data = $data->concat($trendingProducts);
            
            // Add empty row between sections
            $data->push([
                'id' => '',
                'title' => '',
                'sku' => '',
                'barcode' => '',
                'meta.isbn_13' => '',
                'sales_count' => ''
            ]);
        }

        return $data;
    }

    /**
     * Get product frequencies for the given date range
     */
    private function getProductFrequencies(Carbon $start, Carbon $end)
    {
        // Optimized approach: Process orders in chunks to handle large datasets efficiently
        $productCounts = collect();

        Order::whereBetween('created_at', [$start, $end])
            ->whereNotNull('product_ids')
            ->select(['product_ids'])
            ->chunk(1000, function ($orders) use ($productCounts) {
                foreach ($orders as $order) {
                    $productIds = $order->product_ids;
                    if (is_array($productIds)) {
                        // Count each product only once per order (ignore quantity)
                        $uniqueProductIds = array_unique($productIds);
                        foreach ($uniqueProductIds as $productId) {
                            if ($productId) {
                                $productCounts->put($productId, $productCounts->get($productId, 0) + 1);
                            }
                        }
                    }
                }
            });

        // Sort by frequency (descending)
        $productCounts = $productCounts->sortDesc();

        // Get product details in batches to avoid memory issues
        $productIds = $productCounts->keys()->toArray();
        $products = collect();

        // Process products in chunks of 500 to avoid query size limits
        collect($productIds)->chunk(500)->each(function ($chunk) use (&$products) {
            $chunkProducts = Product::whereIn('id', $chunk->toArray())
                ->get(['id', 'title', 'sku', 'barcode', 'meta']);
            $products = $products->merge($chunkProducts);
        });


        // Format for CSV output
        return $productCounts->map(function ($count, $productId) use ($products) {
            $product = $products->get($productId);

            if (!$product) {
                return null;
            }

            return [
                'id' => $product->id,
                'title' => $product->title,
                'sku' => $product->sku,
                'barcode' => $product->barcode,
                'meta.isbn_13' => data_get($product->meta, 'isbn_13') ?? data_get($product->meta, 'ISBN 13'),
                'sales_count' => $count
            ];
        })->filter()->values();
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Filter Type', 'filter_type')
                ->options([
                    'date_range' => 'Date Range',
                    'day_ranges' => 'Last X Days'
                ])
                ->default('date_range')
                ->help('Choose how to filter the trending products data'),

            Date::make('Start Date', 'start_date')
                ->rules('required_if:filter_type,date_range', 'date')
                ->dependsOn('filter_type', function (Date $field, NovaRequest $request, $formData) {
                    if ($formData->filter_type === 'date_range') {
                        $field->show();
                    } else {
                        $field->hide();
                    }
                }),

            Date::make('End Date', 'end_date')
                ->rules('required_if:filter_type,date_range', 'date')
                ->dependsOn('filter_type', function (Date $field, NovaRequest $request, $formData) {
                    if ($formData->filter_type === 'date_range') {
                        $field->show();
                    } else {
                        $field->hide();
                    }
                }),

            Text::make('Day Ranges', 'day_ranges')
                ->placeholder('30,60,90')
                ->help('Enter comma-separated list of day counts (e.g., 30,60,90 for Last 30 days, Last 60 days, Last 90 days)')
                ->rules('required_if:filter_type,day_ranges', 'regex:/^[0-9]+(,[0-9]+)*$/')
                ->dependsOn('filter_type', function (Text $field, NovaRequest $request, $formData) {
                    if ($formData->filter_type === 'day_ranges') {
                        $field->show();
                    } else {
                        $field->hide();
                    }
                })
        ];
    }
}
