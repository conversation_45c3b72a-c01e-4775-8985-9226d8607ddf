// Image URL Field for Laravel Nova
Nova.booting((app, store) => {
  // Index Field Component
  app.component('index-image-url-field', {
    props: ['resourceName', 'field'],
    template: '<span>-</span>'
  });

  // Detail Field Component
  app.component('detail-image-url-field', {
    props: ['resourceName', 'field'],
    template: '<span>-</span>'
  });

  // Form Field Component
  app.component('form-image-url-field', {
    mixins: [Nova.FormField, Nova.HandlesValidationErrors],
    props: ['resourceName', 'resourceId', 'field'],
    data() {
      return {
        processingUrls: false,
      }
    },
    template: `
      <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
      >
        <template #field>
          <textarea
            :id="field.uniqueKey"
            :dusk="field.attribute"
            v-model="value"
            class="w-full form-control form-input form-control-bordered py-3 h-auto"
            :class="errorClasses"
            :placeholder="field.placeholder"
            rows="3"
            :disabled="isReadonly"
          />

          <div v-if="field.helpText" class="help-text help-text mt-2">
            {{ field.helpText }}
          </div>

          <div v-if="processingUrls" class="mt-2 text-sm text-gray-600">
            <div class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing image URLs...
            </div>
          </div>
        </template>
      </DefaultField>
    `,
    methods: {
      setInitialValue() {
        this.value = this.field.value || '';
      },
      fill(formData) {
        formData.append(this.field.attribute, this.value || '');
      },
      handleChange(value) {
        this.value = value;
      },
    },
  });
});
