{"name": "capitalc/image-url-field", "description": "A Laravel Nova field for adding images from URLs.", "keywords": ["laravel", "nova", "image", "url"], "license": "MIT", "require": {"php": ">=7.1.0"}, "autoload": {"psr-4": {"Capitalc\\ImageUrlField\\": "src/"}}, "extra": {"laravel": {"providers": ["Capitalc\\ImageUrlField\\FieldServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}