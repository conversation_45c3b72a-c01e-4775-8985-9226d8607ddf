<template>
  <DefaultField
    :field="field"
    :errors="errors"
    :show-help-text="showHelpText"
    :full-width-content="fullWidthContent"
  >
    <template #field>
      <textarea
        :id="field.uniqueKey"
        :dusk="field.attribute"
        v-model="value"
        class="w-full form-control form-input form-control-bordered py-3 h-auto"
        :class="errorClasses"
        :placeholder="field.placeholder"
        rows="3"
        :disabled="isReadonly"
      />
      
      <div v-if="field.helpText" class="help-text help-text mt-2">
        {{ field.helpText }}
      </div>
      
    </template>
  </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova'

export default {
  mixins: [FormField, HandlesValidationErrors],

  props: ['resourceName', 'resourceId', 'field'],

  data() {
    return {
    }
  },

  methods: {
    /*
     * Set the initial, internal value for the field.
     */
    setInitialValue() {
      this.value = this.field.value || ''
    },

    /**
     * Fill the given FormData object with the field's internal value.
     */
    fill(formData) {
      formData.append(this.field.attribute, this.value || '')
    },

    /**
     * Update the field's internal value.
     */
    handleChange(value) {
      this.value = value
    },
  },
}
</script>

<style>
.help-text {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.25rem;
}
</style>
